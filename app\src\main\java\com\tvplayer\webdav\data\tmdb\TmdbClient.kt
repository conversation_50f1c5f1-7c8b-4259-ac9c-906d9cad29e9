package com.tvplayer.webdav.data.tmdb

import android.util.Log
import com.tvplayer.webdav.data.model.MediaItem
import com.tvplayer.webdav.data.model.MediaType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TMDB客户端
 * 处理媒体信息刮削逻辑
 */
@Singleton
class TmdbClient @Inject constructor(
    private val apiService: TmdbApiService
) {
    companion object {
        private const val TAG = "TmdbClient"

        // ⚠️ 重要：请替换为你的TMDB API密钥
        // 1. 访问 https://www.themoviedb.org/
        // 2. 注册账户并申请API密钥
        // 3. 将下面的 "YOUR_TMDB_API_KEY" 替换为实际的API密钥
        private const val API_KEY = "e5ea1ff22ac53933400bc0251fff5943"

        private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    }

    /**
     * 解析后的电视剧目录信息
     */
    private data class ParsedSeriesInfo(
        val title: String,      // 提取的剧名
        val year: Int? = null,  // 提取的年份
        val metadata: List<String> = emptyList()  // 其他元数据（质量、来源等）
    )

    /**
     * 刮削电影信息
     * @param fileName 电影文件名
     * @param filePath 文件路径
     * @param fileSize 文件大小
     * @return 刮削的媒体信息
     */
    suspend fun scrapeMovie(fileName: String, filePath: String, fileSize: Long): MediaItem? {
        return withContext(Dispatchers.IO) {
            try {
                val year = extractYear(fileName)
                val candidates = generateMovieCandidates(fileName)
                Log.d(TAG, "Movie candidates: ${candidates.joinToString()} (year=$year)")

                for (candidate in candidates) {
                    // 先 zh-CN，若无结果再 en-US
                    val searchZh = apiService.searchMovies(
                        apiKey = API_KEY,
                        query = candidate,
                        year = year
                    )
                    var movies = if (searchZh.isSuccessful) searchZh.body()?.results else null
                    if (movies.isNullOrEmpty()) {
                        val searchEn = apiService.searchMovies(
                            apiKey = API_KEY,
                            query = candidate,
                            language = "en-US",
                            year = year
                        )
                        if (searchEn.isSuccessful) {
                            movies = searchEn.body()?.results
                        }
                    }
                    if (!movies.isNullOrEmpty()) {
                        val movie = movies.first()
                        val detailsResponse = apiService.getMovieDetails(movie.id, API_KEY)
                        val movieDetails = detailsResponse.body() ?: movie

                        // 确保中文标题/概述：若当前语言非中文，尝试读取 zh-CN 翻译数据
                        val finalized = ensureChineseMovie(movie.id, movieDetails)
                        Log.d(TAG, "Movie matched by candidate: $candidate")
                        return@withContext convertTmdbMovieToMediaItem(finalized, filePath, fileSize)
                    }
                }

                Log.w(TAG, "No movie found for any candidate of: $fileName")
                null
            } catch (e: Exception) {
                Log.e(TAG, "Error scraping movie: $fileName", e)
                null
            }
        }
    }

    /**
     * 刮削电视剧信息
     * @param seriesName 电视剧名称（通常是目录名）
     * @param seasonNumber 季数
     * @param episodeNumber 集数
     * @param filePath 文件路径
     * @param fileSize 文件大小
     * @return 刮削的媒体信息
     */
    suspend fun scrapeTVShow(
        seriesName: String,
        seasonNumber: Int?,
        episodeNumber: Int?,
        filePath: String,
        fileSize: Long
    ): MediaItem? {
        return withContext(Dispatchers.IO) {
            try {
                // 解析目录名获取剧名和年份信息
                val parsedInfo = parseSeriesDirectoryName(seriesName)
                val candidates = generateTVCandidates(seriesName)
                Log.d(TAG, "TV candidates: ${candidates.joinToString()} (year=${parsedInfo.year}) S${seasonNumber}E${episodeNumber}")

                var tvShows: List<TmdbTVShow>? = null
                var matchedCandidate: String? = null
                candidateLoop@ for (candidate in candidates) {
                    // 先 zh-CN，若无结果再 en-US
                    val searchZh = apiService.searchTVShows(
                        apiKey = API_KEY,
                        query = candidate,
                        firstAirDateYear = parsedInfo.year
                    )
                    tvShows = if (searchZh.isSuccessful) searchZh.body()?.results else null
                    if (tvShows.isNullOrEmpty()) {
                        val searchEn = apiService.searchTVShows(
                            apiKey = API_KEY,
                            query = candidate,
                            language = "en-US",
                            firstAirDateYear = parsedInfo.year
                        )
                        if (searchEn.isSuccessful) {
                            tvShows = searchEn.body()?.results
                        }
                    }
                    if (!tvShows.isNullOrEmpty()) { matchedCandidate = candidate; break@candidateLoop }
                }

                if (!tvShows.isNullOrEmpty()) {
                    val tvShow = tvShows.first()

                    // 获取详细信息（保持 zh-CN），若缺中文再用 translations 兜底
                    val detailsResponse = apiService.getTVShowDetails(tvShow.id, API_KEY)
                    val tvDetails = ensureChineseTV(tvShow.id, detailsResponse.body() ?: tvShow)

                    // 如果有季和集信息，获取具体剧集信息
                    var episodeTitle: String? = null
                    var episodeOverview: String? = null
                    var episodeRuntime: Long = 0L

                    if (seasonNumber != null && episodeNumber != null) {
                        try {
                            val seasonResponse = apiService.getSeasonDetails(
                                tvShow.id, seasonNumber, API_KEY
                            )
                            val season = seasonResponse.body()
                            val episode = season?.episodes?.find { it.episodeNumber == episodeNumber }

                            episodeTitle = episode?.name
                            episodeOverview = episode?.overview
                            episodeRuntime = (episode?.runtime ?: 0) * 60L // 转换为秒
                        } catch (e: Exception) {
                            Log.w(TAG, "Failed to get episode details", e)
                        }
                    }

                    return@withContext convertTmdbTVToMediaItem(
                        tvDetails, filePath, seasonNumber, episodeNumber,
                        episodeTitle, episodeOverview, episodeRuntime, fileSize
                    )
                }

                Log.w(TAG, "No TV show found for: $seriesName")
                null
            } catch (e: Exception) {
                Log.e(TAG, "Error scraping TV show: $seriesName", e)
                null
            }
        }
    }

    /**
     * 生成电影搜索候选：
     * 1) 清洗标题（去扩展名/噪声/分隔符）
     * 2) 基于 tokens 逐步组合（单词→前缀组合）
     */
    private fun generateMovieCandidates(fileName: String): List<String> {
        // 先清洗，再按“最长→逐步减词”生成候选，避免无意义请求
        val base = cleanMovieTitle(fileName)
        val tokens = base.split(Regex("[\\s._-]+")).filter { it.isNotBlank() }
        if (tokens.isEmpty()) return listOf(base)
        val candidates = mutableListOf<String>()
        for (i in tokens.size downTo 1) {
            candidates.add(tokens.subList(0, i).joinToString(" "))
        }
        return candidates.distinct().take(15)
    }

    /**
     * 智能解析电视剧目录名称
     * 处理各种常见的目录命名模式，提取纯净的剧名
     */
    private fun parseSeriesDirectoryName(directoryName: String): ParsedSeriesInfo {
        var workingTitle = directoryName.trim()
        val extractedMetadata = mutableListOf<String>()
        var extractedYear: Int? = null

        // 1. 移除文件扩展名（如果有）
        workingTitle = workingTitle.substringBeforeLast('.')

        // 2. 处理括号和方括号内的内容（通常是质量、来源等信息）
        val bracketPattern = Regex("[\\[\\(]([^\\]\\)]*)[\\]\\)]")
        val bracketMatches = bracketPattern.findAll(workingTitle).toList()
        for (match in bracketMatches) {
            val content = match.groupValues[1].trim()
            // 检查是否是年份
            val yearMatch = Regex("(19|20)\\d{2}").find(content)
            if (yearMatch != null && extractedYear == null) {
                extractedYear = yearMatch.value.toIntOrNull()
            } else {
                extractedMetadata.add(content)
            }
        }
        workingTitle = workingTitle.replace(bracketPattern, "").trim()

        // 3. 处理末尾的年份模式（如 "凡人修仙传 2025"）
        val yearAtEndPattern = Regex("\\s+(19|20)\\d{2}\\s*$")
        val yearAtEndMatch = yearAtEndPattern.find(workingTitle)
        if (yearAtEndMatch != null && extractedYear == null) {
            extractedYear = yearAtEndMatch.value.trim().toIntOrNull()
            workingTitle = workingTitle.replace(yearAtEndPattern, "").trim()
        }

        // 4. 处理其他年份模式（如 "Series.Name.2025"）
        if (extractedYear == null) {
            val yearPattern = Regex("\\b(19|20)\\d{2}\\b")
            val yearMatch = yearPattern.find(workingTitle)
            if (yearMatch != null) {
                extractedYear = yearMatch.value.toIntOrNull()
                workingTitle = workingTitle.replace(yearPattern, "").trim()
            }
        }

        // 5. 移除常见的质量和编码标识
        val qualityPattern = Regex("\\b(1080p|720p|480p|4K|UHD|HDR|BluRay|WEB-DL|WEBRip|DVDRip|BDRip|x264|x265|H264|H265|HEVC|AAC|DTS|AC3)\\b", RegexOption.IGNORE_CASE)
        workingTitle = workingTitle.replace(qualityPattern, "").trim()

        // 6. 移除季信息（Season、S01等）
        val seasonPattern = Regex("\\b(Season|S)\\s*\\d+\\b", RegexOption.IGNORE_CASE)
        workingTitle = workingTitle.replace(seasonPattern, "").trim()

        // 7. 移除常见的发布组标识（通常在末尾）
        val releaseGroupPattern = Regex("\\b(RARBG|YTS|ETRG|FGT|SPARKS|AMZN|NF|HULU|HBO|DSNP|NETFLIX|AMAZON|DISNEY)\\b", RegexOption.IGNORE_CASE)
        workingTitle = workingTitle.replace(releaseGroupPattern, "").trim()

        // 8. 处理中英文混合标题（如 "权力的游戏 Game of Thrones"）
        // 保留中文部分作为主标题，英文部分作为备选
        val chineseEnglishPattern = Regex("^([\\u4e00-\\u9fff\\s]+)\\s+([A-Za-z\\s]+)$")
        val chineseEnglishMatch = chineseEnglishPattern.find(workingTitle)
        if (chineseEnglishMatch != null) {
            val chinesePart = chineseEnglishMatch.groupValues[1].trim()
            val englishPart = chineseEnglishMatch.groupValues[2].trim()
            // 优先使用中文标题，如果中文部分足够长
            workingTitle = if (chinesePart.length >= 2) chinesePart else englishPart
        }

        // 9. 标准化分隔符（将 . _ - 替换为空格）
        workingTitle = workingTitle.replace(Regex("[._-]+"), " ")

        // 10. 清理多余的空格
        workingTitle = workingTitle.replace(Regex("\\s+"), " ").trim()

        // 11. 处理特殊情况：如果标题为空或太短，使用原始名称
        if (workingTitle.isBlank() || workingTitle.length < 2) {
            workingTitle = directoryName.replace(Regex("[._-]+"), " ").trim()
        }

        Log.d(TAG, "Parsed series directory: '$directoryName' -> title='$workingTitle', year=$extractedYear")

        return ParsedSeriesInfo(
            title = workingTitle,
            year = extractedYear,
            metadata = extractedMetadata
        )
    }

    /**
     * 测试目录名解析功能（用于调试和验证）
     */
    fun testDirectoryParsing() {
        val testCases = listOf(
            "凡人修仙传 2025",
            "Game of Thrones (2011)",
            "Breaking.Bad.2008.1080p",
            "Friends S01",
            "The.Office.US.2005.Season.1",
            "Stranger Things [2016] 4K",
            "House.of.Cards.2013.WEB-DL",
            "权力的游戏",
            "老友记.Friends.1994-2004",
            "绝命毒师 Breaking Bad 2008",
            "权力的游戏 Game of Thrones",
            "三体.2023.4K.HDR",
            "The Mandalorian (2019) Season 1",
            "西部世界.Westworld.2016.BluRay",
            "纸牌屋 House of Cards 2013"
        )

        Log.d(TAG, "=== Directory Parsing Test Results ===")
        testCases.forEach { testCase ->
            val result = parseSeriesDirectoryName(testCase)
            Log.d(TAG, "Input: '$testCase' -> Title: '${result.title}', Year: ${result.year}")
        }
        Log.d(TAG, "=== End Test Results ===")
    }
    }

    /**
     * 生成电视剧搜索候选：基于目录名智能解析 + token 前缀组合
     */
    private fun generateTVCandidates(seriesName: String): List<String> {
        // 先清洗（去 Season/Sxx 等），再按“最长→逐步减词”生成候选
        // 先智能解析目录名，提取纯净的剧名
        val parsedTitle = parseSeriesDirectoryName(seriesName)
        val base = cleanTVTitle(parsedTitle.title)
        val tokens = base.split(Regex("[\\s._-]+")).filter { it.isNotBlank() }
        if (tokens.isEmpty()) return listOf(base)

        val candidates = mutableListOf<String>()
        // 添加完整的解析后标题作为首选候选
        candidates.add(base)

        // 按"最长→逐步减词"生成候选
        for (i in tokens.size - 1 downTo 1) {
            candidates.add(tokens.subList(0, i).joinToString(" "))
        }
        return candidates.distinct().take(15)
    }

    private fun cleanMovieTitle(fileName: String): String {
        return fileName
            .substringBeforeLast('.') // 移除扩展名
            .replace(Regex("\\.(\\d{4})"), " $1") // 处理年份
            .replace(Regex("[\\[\\(].*?[\\]\\)]"), "") // 移除括号内容
            .replace(Regex("\\b(1080p|720p|480p|4K|UHD|HDR|BluRay|WEB-DL|WEBRip|DVDRip)\\b", RegexOption.IGNORE_CASE), "")
            .replace(Regex("\\b(x264|x265|H264|H265|HEVC)\\b", RegexOption.IGNORE_CASE), "")
            .replace(Regex("[._-]"), " ")
            .replace(Regex("\\s+"), " ")
            .trim()
    }


    private fun extractYear(fileName: String): Int? {
        // 提取 1900-2099 年份
        val match = Regex("(?:\\D|^)(19|20)\\d{2}(?:\\D|$)").find(fileName)
        return match?.value?.filter { it.isDigit() }?.takeLast(4)?.toIntOrNull()
    }

    /**
     * 清理电视剧标题（轻量级清理，主要处理已经解析过的标题）
     */
    private fun cleanTVTitle(seriesName: String): String {
        return seriesName
            .replace(Regex("\\s+"), " ")  // 标准化空格
            .trim()
    }

    /**
     * 转换TMDB电影数据为MediaItem
     */
    private fun convertTmdbMovieToMediaItem(movie: TmdbMovie, filePath: String, fileSize: Long): MediaItem {
        val releaseDate = try {
            movie.releaseDate?.let { dateFormat.parse(it) }
        } catch (e: Exception) {
            null
        }

        return MediaItem(
            id = "movie_${movie.id}",
            title = movie.title,
            originalTitle = movie.originalTitle,
            overview = movie.overview,
            posterPath = movie.posterPath?.let { "${TmdbApiService.IMAGE_BASE_URL}${TmdbApiService.POSTER_SIZE_W500}$it" },
            backdropPath = movie.backdropPath?.let { "${TmdbApiService.IMAGE_BASE_URL}${TmdbApiService.BACKDROP_SIZE_W1280}$it" },
            releaseDate = releaseDate,
            rating = movie.voteAverage,
            duration = (movie.runtime ?: 0) * 60L, // 转换为秒
            mediaType = MediaType.MOVIE,
            filePath = filePath,
            fileSize = fileSize,
            genre = movie.genres?.map { it.name } ?: emptyList()
        )
    }

    /**
     * 转换TMDB电视剧数据为MediaItem
     */
    private fun convertTmdbTVToMediaItem(
        tvShow: TmdbTVShow,
        filePath: String,
        seasonNumber: Int?,
        episodeNumber: Int?,
        episodeTitle: String?,
        episodeOverview: String?,
        episodeRuntime: Long,
        fileSize: Long
    ): MediaItem {
        val releaseDate = try {
            tvShow.firstAirDate?.let { dateFormat.parse(it) }
        } catch (e: Exception) {
            null
        }

        return MediaItem(
            id = "tv_${tvShow.id}_s${seasonNumber}_e${episodeNumber}",
            title = episodeTitle ?: "第${episodeNumber}集",
            originalTitle = tvShow.originalName,
            overview = episodeOverview ?: tvShow.overview,
            posterPath = tvShow.posterPath?.let { "${TmdbApiService.IMAGE_BASE_URL}${TmdbApiService.POSTER_SIZE_W500}$it" },
            backdropPath = tvShow.backdropPath?.let { "${TmdbApiService.IMAGE_BASE_URL}${TmdbApiService.BACKDROP_SIZE_W1280}$it" },
            releaseDate = releaseDate,
            rating = tvShow.voteAverage,
            duration = episodeRuntime,
            mediaType = MediaType.TV_EPISODE,
            filePath = filePath,
            fileSize = fileSize,
            seasonNumber = seasonNumber,
            episodeNumber = episodeNumber,
            seriesId = "tv_${tvShow.id}",
            seriesTitle = tvShow.name,
            genre = tvShow.genres?.map { it.name } ?: emptyList()
        )
    }

    /**
     * 确保返回中文（zh-CN）字段：title/name/overview
     * 若 getDetails 已经是 zh-CN，则直接返回；否则从 translations 中找 zh-CN 数据覆盖
     */
    private suspend fun ensureChineseMovie(movieId: Int, details: TmdbMovie): TmdbMovie {
        return withContext(Dispatchers.IO) {
            try {
                val tr = apiService.getMovieTranslations(movieId, API_KEY)
                val cn = tr.body()?.translations?.firstOrNull { it.iso_639_1?.equals("zh", true) == true }
                if (cn?.data != null) {
                    val zhTitle = cn.data.title
                    val zhOverview = cn.data.overview
                    details.copy(
                        title = if (!zhTitle.isNullOrBlank()) zhTitle else details.title,
                        overview = if (!zhOverview.isNullOrBlank()) zhOverview else details.overview
                    )
                } else details
            } catch (e: Exception) {
                Log.w(TAG, "ensureChineseMovie failed", e)
                details
            }
        }
    }

    private suspend fun ensureChineseTV(tvId: Int, details: TmdbTVShow): TmdbTVShow {
        return withContext(Dispatchers.IO) {
            try {
                val tr = apiService.getTVTranslations(tvId, API_KEY)
                val cn = tr.body()?.translations?.firstOrNull { it.iso_639_1?.equals("zh", true) == true }
                if (cn?.data != null) {
                    val zhName = cn.data.name
                    val zhOverview = cn.data.overview
                    details.copy(
                        name = if (!zhName.isNullOrBlank()) zhName else details.name,
                        overview = if (!zhOverview.isNullOrBlank()) zhOverview else details.overview
                    )
                } else details
            } catch (e: Exception) {
                Log.w(TAG, "ensureChineseTV failed", e)
                details
            }
        }
    }

    /**
     * 获取完整的图片URL
     */
    fun getFullImageUrl(imagePath: String?, size: String = TmdbApiService.POSTER_SIZE_W500): String? {
        return imagePath?.let { "${TmdbApiService.IMAGE_BASE_URL}$size$it" }
    }

    /**
     * 获取剧集静态图片URL
     */
    fun getEpisodeStillUrl(stillPath: String?): String? {
        return stillPath?.let { "${TmdbApiService.IMAGE_BASE_URL}${TmdbApiService.BACKDROP_SIZE_W780}$it" }
    }

    /**
     * 获取电影详细信息（包括演员）
     * @param movieId 电影ID
     * @return 电影详细信息
     */
    suspend fun getMovieDetailsWithCast(movieId: Int): Pair<TmdbMovie, List<TmdbCast>>? {
        return withContext(Dispatchers.IO) {
            try {
                // 获取电影详情
                val detailsResponse = apiService.getMovieDetails(movieId, API_KEY)
                val movieDetails = detailsResponse.body()
                
                if (movieDetails != null) {
                    // 获取演员信息
                    val castResponse = apiService.getMovieCredits(movieId, API_KEY)
                    val cast = castResponse.body()?.cast ?: emptyList()
                    
                    // 确保中文标题/概述
                    val finalizedMovie = ensureChineseMovie(movieId, movieDetails)
                    
                    Pair(finalizedMovie, cast)
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting movie details with cast: $movieId", e)
                null
            }
        }
    }

    /**
     * 获取电视剧详细信息（包括演员）
     * @param tvId 电视剧ID
     * @return 电视剧详细信息和演员列表
     */
    suspend fun getTVShowDetailsWithCast(tvId: Int): Pair<TmdbTVShow, List<TmdbCast>>? {
        return withContext(Dispatchers.IO) {
            try {
                // 获取电视剧详情
                val detailsResponse = apiService.getTVShowDetails(tvId, API_KEY)
                val tvDetails = detailsResponse.body()

                if (tvDetails != null) {
                    // 获取演员信息
                    val castResponse = apiService.getTVCredits(tvId, API_KEY)
                    val cast = castResponse.body()?.cast ?: emptyList()

                    // 确保中文标题/概述
                    val finalizedTV = ensureChineseTV(tvId, tvDetails)

                    Pair(finalizedTV, cast)
                } else {
                    null
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting TV show details with cast: $tvId", e)
                null
            }
        }
    }

    /**
     * 获取电视剧季详情（包括剧集图片）
     * @param tvId 电视剧ID
     * @param seasonNumber 季数
     * @return 季详情信息
     */
    suspend fun getSeasonDetailsWithImages(tvId: Int, seasonNumber: Int): TmdbSeasonDetails? {
        return withContext(Dispatchers.IO) {
            try {
                val response = apiService.getSeasonDetails(tvId, seasonNumber, API_KEY)
                response.body()
            } catch (e: Exception) {
                Log.e(TAG, "Error getting season details: $tvId S$seasonNumber", e)
                null
            }
        }
    }

    /**
     * 获取电视剧所有季的信息
     * @param tvId 电视剧ID
     * @return 所有季的列表
     */
    suspend fun getAllSeasonsForTVShow(tvId: Int): List<TmdbSeason>? {
        return withContext(Dispatchers.IO) {
            try {
                val response = apiService.getTVShowDetails(tvId, API_KEY)
                val tvDetails = response.body()
                tvDetails?.seasons?.filter { it.seasonNumber > 0 } // 过滤掉特别篇（season 0）
            } catch (e: Exception) {
                Log.e(TAG, "Error getting seasons for TV show: $tvId", e)
                null
            }
        }
    }

    /**
     * 转换TMDB演员信息为Actor模型
     * @param cast TMDB演员信息
     * @return Actor模型
     */
    private fun convertTmdbCastToActor(cast: TmdbCast): com.tvplayer.webdav.data.model.Actor {
        return com.tvplayer.webdav.data.model.Actor(
            id = cast.id.toString(),
            name = cast.name,
            role = cast.character,
            avatarUrl = cast.profilePath?.let { "${TmdbApiService.IMAGE_BASE_URL}${TmdbApiService.POSTER_SIZE_W500}$it" },
            isDirector = false
        )
    }

    /**
     * 获取电影演员列表
     * @param movieId 电影ID
     * @return 演员列表
     */
    suspend fun getMovieActors(movieId: Int): List<com.tvplayer.webdav.data.model.Actor>? {
        return withContext(Dispatchers.IO) {
            try {
                val castResponse = apiService.getMovieCredits(movieId, API_KEY)
                val cast = castResponse.body()?.cast ?: emptyList()
                
                // 转换为Actor模型，只取前10个主要演员
                cast.sortedBy { it.order }
                    .take(10)
                    .map { convertTmdbCastToActor(it) }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting movie actors: $movieId", e)
                null
            }
        }
    }

    /**
     * 获取电视剧演员列表
     * @param tvId 电视剧ID
     * @return 演员列表
     */
    suspend fun getTVShowActors(tvId: Int): List<com.tvplayer.webdav.data.model.Actor>? {
        return withContext(Dispatchers.IO) {
            try {
                val castResponse = apiService.getTVCredits(tvId, API_KEY)
                val cast = castResponse.body()?.cast ?: emptyList()
                
                // 转换为Actor模型，只取前10个主要演员
                cast.sortedBy { it.order }
                    .take(10)
                    .map { convertTmdbCastToActor(it) }
            } catch (e: Exception) {
                Log.e(TAG, "Error getting TV show actors: $tvId", e)
                null
            }
        }
    }
}
